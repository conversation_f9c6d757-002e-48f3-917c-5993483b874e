package resolvers

import (
	"context"
	"encoding/json"
	"fmt"
	"math"
	"strconv"
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	repo_activity_cashback "gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo/activity_cashback"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo/agent_referral"
	"go.uber.org/zap"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/controller/admin/graphql/gql_model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/service/activity_cashback"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/utils"
)

// AdminActivityCashbackResolver handles admin-specific activity cashback operations
type AdminActivityCashbackResolver struct {
	adminService activity_cashback.AdminServiceInterface
}

// NewAdminActivityCashbackResolver creates a new AdminActivityCashbackResolver
func NewAdminActivityCashbackResolver() *AdminActivityCashbackResolver {
	return &AdminActivityCashbackResolver{
		adminService: activity_cashback.NewAdminService(),
	}
}

// CreateTask creates a new activity task (Admin only)
func (r *AdminActivityCashbackResolver) CreateTask(ctx context.Context, input gql_model.CreateTaskInput) (*gql_model.ActivityTask, error) {
	// Parse CategoryID as uint
	categoryID, err := strconv.ParseUint(input.CategoryID, 10, 32)
	if err != nil {
		return nil, fmt.Errorf("invalid category ID: %w", err)
	}

	// Convert multilingual name input to model
	multilingualName := &model.MultilingualName{
		En: input.Name.En,
		Zh: input.Name.Zh,
		Ja: input.Name.Ja,
		Hi: input.Name.Hi,
		Hk: input.Name.Hk,
		Vi: input.Name.Vi,
	}

	// Create task model
	task := &model.ActivityTask{
		CategoryID: uint(categoryID),
		Name:       input.Name.En, // Set legacy field for backward compatibility

		Frequency: model.TaskFrequency(input.Frequency), // Direct cast now works since enums match
		Points:    input.Points,
		SortOrder: 0, // Default value
		IsActive:  true,
	}

	// Set multilingual name
	task.SetMultilingualName(multilingualName)

	// Set optional fields
	if input.Description != nil {
		task.Description = input.Description
	}
	if input.TaskIdentifier != nil {
		taskIdentifier := model.TaskIdentifier(*input.TaskIdentifier)
		// Validate that the identifier is valid
		if model.IsValidTaskIdentifier(taskIdentifier) {
			task.TaskIdentifier = &taskIdentifier
		} else {
			return nil, fmt.Errorf("invalid task identifier: %s", string(*input.TaskIdentifier))
		}
	}
	if input.MaxCompletions != nil {
		task.MaxCompletions = input.MaxCompletions
	}
	if input.ResetPeriod != nil {
		resetPeriod := model.ResetPeriod(*input.ResetPeriod)
		task.ResetPeriod = &resetPeriod
	}
	if input.Conditions != nil {
		var conditions model.TaskConditions
		if err := json.Unmarshal([]byte(*input.Conditions), &conditions); err != nil {
			return nil, fmt.Errorf("invalid conditions format: %w", err)
		}
		task.Conditions = &conditions
	}
	if input.ActionTarget != nil {
		task.ActionTarget = input.ActionTarget
	}
	if input.VerificationMethod != nil {
		verificationMethod := model.VerificationMethod(*input.VerificationMethod)
		task.VerificationMethod = &verificationMethod
	}
	if input.ExternalLink != nil {
		task.ExternalLink = input.ExternalLink
	}
	if input.TaskIcon != nil {
		task.TaskIcon = input.TaskIcon
	}
	if input.ButtonText != nil {
		task.ButtonText = input.ButtonText
	}
	if input.StartDate != nil {
		task.StartDate = utils.TimestampToTime(input.StartDate)
	}
	if input.EndDate != nil {
		task.EndDate = utils.TimestampToTime(input.EndDate)
	}
	if input.SortOrder != nil {
		task.SortOrder = *input.SortOrder
	}

	// Get admin ID from context (assuming it's set by middleware)
	userID := ctx.Value("userId")
	if userID == nil {
		return nil, fmt.Errorf("admin ID not found in context")
	}

	userIDStr, ok := userID.(string)
	if !ok {
		return nil, fmt.Errorf("admin ID not found in context")
	}

	adminID, err := uuid.Parse(userIDStr)
	if err != nil {
		return nil, fmt.Errorf("invalid admin ID: %w", err)
	}

	// Create task using admin service
	err = r.adminService.CreateTask(ctx, task, adminID)
	if err != nil {
		global.GVA_LOG.Error("Failed to create task", zap.Error(err))
		return nil, fmt.Errorf("failed to create task: %w", err)
	}

	// Load the category to populate the category field
	categories, err := r.adminService.GetTaskCategories(ctx)
	if err != nil {
		global.GVA_LOG.Error("Failed to get task categories", zap.Error(err))
		return nil, fmt.Errorf("failed to get task categories: %w", err)
	}

	// Find the category for this task
	var taskCategory *model.TaskCategory
	for _, cat := range categories {
		if cat.ID == task.CategoryID {
			taskCategory = &cat
			break
		}
	}

	if taskCategory == nil {
		return nil, fmt.Errorf("category not found for task")
	}

	// Convert to GraphQL model with category
	gqlTask := convertActivityTaskToGQL(task)
	gqlTask.Category = convertTaskCategoryToGQL(taskCategory)

	return gqlTask, nil
}

// CreateConsecutiveCheckinTask creates a new consecutive check-in task with configurable milestones (Admin only)
func (r *AdminActivityCashbackResolver) CreateConsecutiveCheckinTask(ctx context.Context, input gql_model.CreateConsecutiveCheckinTaskInput) (*gql_model.ActivityTask, error) {
	// Parse CategoryID as uint
	categoryID, err := strconv.ParseUint(input.CategoryID, 10, 32)
	if err != nil {
		return nil, fmt.Errorf("invalid category ID: %w", err)
	}

	// Convert multilingual name input to model
	multilingualName := &model.MultilingualName{
		En: input.Name.En,
		Zh: input.Name.Zh,
		Ja: input.Name.Ja,
		Hi: input.Name.Hi,
		Hk: input.Name.Hk,
		Vi: input.Name.Vi,
	}

	// Convert milestones from GraphQL input to model
	milestones := make([]model.ConsecutiveCheckinMilestone, len(input.Milestones))
	for i, milestone := range input.Milestones {
		var milestoneName *model.MultilingualName
		if milestone.Name != nil {
			milestoneName = &model.MultilingualName{
				En: milestone.Name.En,
				Zh: milestone.Name.Zh,
				Ja: milestone.Name.Ja,
				Hi: milestone.Name.Hi,
				Hk: milestone.Name.Hk,
				Vi: milestone.Name.Vi,
			}
		}

		milestones[i] = model.ConsecutiveCheckinMilestone{
			Days:     milestone.Days,
			Points:   milestone.Points,
			Name:     milestoneName,
			TaskIcon: milestone.TaskIcon,
		}
	}

	// Create task conditions with milestones
	conditions := &model.TaskConditions{
		ConsecutiveCheckinMilestones: milestones,
	}

	// Create task identifier variable so we can take its address
	taskIdentifier := model.TaskIDConsecutiveCheckinConfigurable

	// Create task model
	task := &model.ActivityTask{
		CategoryID:     uint(categoryID),
		Name:           input.Name.En, // Set legacy field for backward compatibility
		NameData:       multilingualName,
		Frequency:      model.FrequencyProgressive, // Consecutive check-in is progressive (milestone-based)
		TaskIdentifier: &taskIdentifier,
		Points:         0, // Points will be awarded based on milestones
		Conditions:     conditions,
		SortOrder:      0, // Default value
		IsActive:       true,
	}

	// Set optional fields
	if input.Description != nil {
		task.Description = input.Description
	}

	if input.TaskIcon != nil {
		task.TaskIcon = input.TaskIcon
	}

	if input.ButtonText != nil {
		task.ButtonText = input.ButtonText
	}

	if input.ActionTarget != nil {
		task.ActionTarget = input.ActionTarget
	}

	if input.VerificationMethod != nil {
		// Convert string to VerificationMethod enum if needed
		verificationMethod := model.VerificationMethod(*input.VerificationMethod)
		task.VerificationMethod = &verificationMethod
	}

	if input.StartDate != nil {
		startTime := time.Unix(*input.StartDate, 0)
		task.StartDate = &startTime
	}

	if input.EndDate != nil {
		endTime := time.Unix(*input.EndDate, 0)
		task.EndDate = &endTime
	}

	if input.SortOrder != nil {
		task.SortOrder = *input.SortOrder
	}

	// Get admin ID from context
	userID := ctx.Value("userId")
	if userID == nil {
		return nil, fmt.Errorf("admin ID not found in context")
	}

	userIDStr, ok := userID.(string)
	if !ok {
		return nil, fmt.Errorf("admin ID not found in context")
	}

	adminID, err := uuid.Parse(userIDStr)
	if err != nil {
		return nil, fmt.Errorf("invalid admin ID: %w", err)
	}

	// Create task using admin service
	err = r.adminService.CreateTask(ctx, task, adminID)
	if err != nil {
		global.GVA_LOG.Error("Failed to create consecutive check-in task", zap.Error(err))
		return nil, fmt.Errorf("failed to create consecutive check-in task: %w", err)
	}

	// Load the category to populate the category field
	categories, err := r.adminService.GetTaskCategories(ctx)
	if err != nil {
		global.GVA_LOG.Error("Failed to get task categories", zap.Error(err))
		return nil, fmt.Errorf("failed to get task categories: %w", err)
	}

	// Find the category for this task
	var taskCategory *model.TaskCategory
	for _, cat := range categories {
		if cat.ID == task.CategoryID {
			taskCategory = &cat
			break
		}
	}

	if taskCategory == nil {
		return nil, fmt.Errorf("category not found for task")
	}

	// Convert to GraphQL model with category
	gqlTask := convertActivityTaskToGQL(task)
	gqlTask.Category = convertTaskCategoryToGQL(taskCategory)

	return gqlTask, nil
}

// CreateAccumulatedMEMETradingVolumeTask creates a new accumulated MEME trading volume task with custom threshold (Admin only)
func (r *AdminActivityCashbackResolver) CreateAccumulatedMEMETradingVolumeTask(ctx context.Context, input gql_model.CreateAccumulatedMEMETradingVolumeTaskInput) (*gql_model.ActivityTask, error) {
	// Get admin ID from context
	userID := ctx.Value("userId")
	if userID == nil {
		return nil, fmt.Errorf("admin ID not found in context")
	}

	userIDStr, ok := userID.(string)
	if !ok {
		return nil, fmt.Errorf("admin ID not found in context")
	}

	adminID, err := uuid.Parse(userIDStr)
	if err != nil {
		return nil, fmt.Errorf("invalid admin ID: %w", err)
	}

	// Convert GraphQL input to service request
	req := activity_cashback.CreateAccumulatedMEMETradingVolumeTaskRequest{
		VolumeThreshold: input.VolumeThreshold,
		Points:          input.Points,
	}

	// Set optional fields if provided
	if input.CategoryID != nil {
		categoryID, err := strconv.ParseUint(*input.CategoryID, 10, 32)
		if err != nil {
			return nil, fmt.Errorf("invalid category ID: %w", err)
		}
		categoryIDUint := uint(categoryID)
		req.CategoryID = &categoryIDUint
	}

	if input.Name != nil {
		req.Name = &model.MultilingualName{
			En: input.Name.En,
			Zh: input.Name.Zh,
			Ja: input.Name.Ja,
			Hi: input.Name.Hi,
			Hk: input.Name.Hk,
			Vi: input.Name.Vi,
		}
	}

	if input.Description != nil {
		req.Description = input.Description
	}

	if input.MaxCompletions != nil {
		req.MaxCompletions = input.MaxCompletions
	}

	if input.ActionTarget != nil {
		req.ActionTarget = input.ActionTarget
	}

	if input.VerificationMethod != nil {
		verificationMethod := model.VerificationMethod(*input.VerificationMethod)
		req.VerificationMethod = &verificationMethod
	}

	if input.ExternalLink != nil {
		req.ExternalLink = input.ExternalLink
	}

	if input.TaskIcon != nil {
		req.TaskIcon = input.TaskIcon
	}

	if input.ButtonText != nil {
		req.ButtonText = input.ButtonText
	}

	if input.StartDate != nil {
		startDate := time.Unix(*input.StartDate, 0)
		req.StartDate = &startDate
	}

	if input.EndDate != nil {
		endDate := time.Unix(*input.EndDate, 0)
		req.EndDate = &endDate
	}

	if input.SortOrder != nil {
		req.SortOrder = input.SortOrder
	}

	// Create task using admin service
	task, err := r.adminService.CreateAccumulatedMEMETradingVolumeTask(ctx, req, adminID)
	if err != nil {
		global.GVA_LOG.Error("Failed to create accumulated MEME trading volume task", zap.Error(err))
		return nil, fmt.Errorf("failed to create accumulated MEME trading volume task: %w", err)
	}

	// Load the category to populate the category field
	categories, err := r.adminService.GetTaskCategories(ctx)
	if err != nil {
		global.GVA_LOG.Error("Failed to get task categories", zap.Error(err))
		return nil, fmt.Errorf("failed to get task categories: %w", err)
	}

	var taskCategory *model.TaskCategory
	for _, cat := range categories {
		if cat.ID == task.CategoryID {
			taskCategory = &cat
			break
		}
	}

	if taskCategory == nil {
		return nil, fmt.Errorf("category not found for task")
	}

	// Convert to GraphQL model with category
	gqlTask := convertActivityTaskToGQL(task)
	gqlTask.Category = convertTaskCategoryToGQL(taskCategory)

	return gqlTask, nil
}

// UpdateTask updates an existing activity task (Admin only)
func (r *AdminActivityCashbackResolver) UpdateTask(ctx context.Context, input gql_model.UpdateTaskInput) (*gql_model.ActivityTask, error) {
	taskID := uuid.MustParse(input.ID)

	// Get existing task using GetAllTasks and filter by ID
	allTasks, err := r.adminService.GetAllTasks(ctx)
	if err != nil {
		global.GVA_LOG.Error("Failed to get tasks for update", zap.Error(err))
		return nil, fmt.Errorf("failed to get tasks: %w", err)
	}

	var existingTask *model.ActivityTask
	for _, task := range allTasks {
		if task.ID == taskID {
			existingTask = &task
			break
		}
	}

	if existingTask == nil {
		return nil, fmt.Errorf("task not found")
	}

	// Update fields if provided
	if input.CategoryID != nil {
		categoryID, err := strconv.ParseUint(*input.CategoryID, 10, 32)
		if err != nil {
			return nil, fmt.Errorf("invalid category ID: %w", err)
		}
		existingTask.CategoryID = uint(categoryID)
	}
	if input.Name != nil {
		// Convert multilingual name input to model
		multilingualName := &model.MultilingualName{
			En: input.Name.En,
			Zh: input.Name.Zh,
			Ja: input.Name.Ja,
			Hi: input.Name.Hi,
			Hk: input.Name.Hk,
			Vi: input.Name.Vi,
		}
		existingTask.SetMultilingualName(multilingualName)
	}
	if input.Description != nil {
		existingTask.Description = input.Description
	}

	if input.Frequency != nil {
		existingTask.Frequency = model.TaskFrequency(*input.Frequency) // Direct cast now works since enums match
	}
	if input.TaskIdentifier != nil {
		taskIdentifier := model.TaskIdentifier(*input.TaskIdentifier)
		existingTask.TaskIdentifier = &taskIdentifier
	}
	if input.Points != nil {
		existingTask.Points = *input.Points
	}
	if input.MaxCompletions != nil {
		existingTask.MaxCompletions = input.MaxCompletions
	}
	if input.ResetPeriod != nil {
		resetPeriod := model.ResetPeriod(*input.ResetPeriod)
		existingTask.ResetPeriod = &resetPeriod
	}
	if input.Conditions != nil {
		var conditions model.TaskConditions
		if err := json.Unmarshal([]byte(*input.Conditions), &conditions); err != nil {
			return nil, fmt.Errorf("invalid conditions format: %w", err)
		}
		existingTask.Conditions = &conditions
	}
	if input.ActionTarget != nil {
		existingTask.ActionTarget = input.ActionTarget
	}
	if input.VerificationMethod != nil {
		verificationMethod := model.VerificationMethod(*input.VerificationMethod)
		existingTask.VerificationMethod = &verificationMethod
	}
	if input.ExternalLink != nil {
		existingTask.ExternalLink = input.ExternalLink
	}
	if input.TaskIcon != nil {
		existingTask.TaskIcon = input.TaskIcon
	}
	if input.ButtonText != nil {
		existingTask.ButtonText = input.ButtonText
	}
	if input.StartDate != nil {
		existingTask.StartDate = utils.TimestampToTime(input.StartDate)
	}
	if input.EndDate != nil {
		existingTask.EndDate = utils.TimestampToTime(input.EndDate)
	}
	if input.SortOrder != nil {
		existingTask.SortOrder = *input.SortOrder
	}
	if input.IsActive != nil {
		existingTask.IsActive = *input.IsActive
	}

	// Get admin ID from context
	userID := ctx.Value("userId")
	if userID == nil {
		return nil, fmt.Errorf("admin ID not found in context")
	}

	userIDStr, ok := userID.(string)
	if !ok {
		return nil, fmt.Errorf("admin ID not found in context")
	}

	adminID, err := uuid.Parse(userIDStr)
	if err != nil {
		return nil, fmt.Errorf("invalid admin ID: %w", err)
	}

	// Update task using admin service
	err = r.adminService.UpdateTask(ctx, existingTask, adminID)
	if err != nil {
		global.GVA_LOG.Error("Failed to update task", zap.Error(err))
		return nil, fmt.Errorf("failed to update task: %w", err)
	}

	return convertActivityTaskToGQL(existingTask), nil
}

// DeleteTask deletes an activity task (Admin only)
func (r *AdminActivityCashbackResolver) DeleteTask(ctx context.Context, taskID string) (bool, error) {
	taskUUID := uuid.MustParse(taskID)

	err := r.adminService.DeleteTask(ctx, taskUUID)
	if err != nil {
		global.GVA_LOG.Error("Failed to delete task", zap.Error(err))
		return false, fmt.Errorf("failed to delete task: %w", err)
	}

	return true, nil
}

// CreateTaskCategory creates a new task category (Admin only)
func (r *AdminActivityCashbackResolver) CreateTaskCategory(ctx context.Context, input gql_model.CreateTaskCategoryInput) (*gql_model.TaskCategory, error) {
	// Convert admin GraphQL enum to model enum
	modelCategoryName := convertAdminGQLCategoryToModel(input.Name)

	// Create category model
	category := &model.TaskCategory{
		Name:        modelCategoryName,
		DisplayName: input.DisplayName,
		IsActive:    true,
		SortOrder:   0, // Default value
	}

	// Set optional fields
	if input.Description != nil {
		category.Description = input.Description
	}

	if input.Icon != nil {
		category.Icon = input.Icon
	}

	if input.SortOrder != nil {
		category.SortOrder = *input.SortOrder
	}

	// Create category using admin service
	err := r.adminService.CreateTaskCategory(ctx, category)
	if err != nil {
		global.GVA_LOG.Error("Failed to create task category", zap.Error(err))
		return nil, fmt.Errorf("failed to create task category: %w", err)
	}

	return convertTaskCategoryToGQL(category), nil
}

// UpdateTaskCategory updates an existing task category (Admin only)
func (r *AdminActivityCashbackResolver) UpdateTaskCategory(ctx context.Context, input gql_model.UpdateTaskCategoryInput) (*gql_model.TaskCategory, error) {
	// Parse category ID as uint
	categoryIDUint, err := strconv.ParseUint(input.ID, 10, 32)
	if err != nil {
		return nil, fmt.Errorf("invalid category ID: %w", err)
	}

	// Get existing category using GetTaskCategories and filter by ID
	allCategories, err := r.adminService.GetTaskCategories(ctx)
	if err != nil {
		global.GVA_LOG.Error("Failed to get task categories for update", zap.Error(err))
		return nil, fmt.Errorf("failed to get task categories: %w", err)
	}

	var existingCategory *model.TaskCategory
	for _, category := range allCategories {
		if category.ID == uint(categoryIDUint) {
			existingCategory = &category
			break
		}
	}

	if existingCategory == nil {
		return nil, fmt.Errorf("task category not found")
	}

	// Update fields if provided
	if input.Name != nil {
		existingCategory.Name = convertAdminGQLCategoryToModel(*input.Name)
	}
	if input.DisplayName != nil {
		existingCategory.DisplayName = *input.DisplayName
	}
	if input.Description != nil {
		existingCategory.Description = input.Description
	}
	if input.Icon != nil {
		existingCategory.Icon = input.Icon
	}
	if input.IsActive != nil {
		existingCategory.IsActive = *input.IsActive
	}
	if input.SortOrder != nil {
		existingCategory.SortOrder = *input.SortOrder
	}

	// Update category using admin service
	err = r.adminService.UpdateTaskCategory(ctx, existingCategory)
	if err != nil {
		global.GVA_LOG.Error("Failed to update task category", zap.Error(err))
		return nil, fmt.Errorf("failed to update task category: %w", err)
	}

	return convertTaskCategoryToGQL(existingCategory), nil
}

// DeleteTaskCategory deletes a task category (Admin only)
func (r *AdminActivityCashbackResolver) DeleteTaskCategory(ctx context.Context, categoryID string) (bool, error) {
	// Parse category ID as uint
	categoryIDUint, err := strconv.ParseUint(categoryID, 10, 32)
	if err != nil {
		return false, fmt.Errorf("invalid category ID: %w", err)
	}

	err = r.adminService.DeleteTaskCategory(ctx, uint(categoryIDUint))
	if err != nil {
		global.GVA_LOG.Error("Failed to delete task category", zap.Error(err))
		return false, fmt.Errorf("failed to delete task category: %w", err)
	}

	return true, nil
}

// CreateTierBenefit creates a new tier benefit (Admin only)
func (r *AdminActivityCashbackResolver) CreateTierBenefit(ctx context.Context, input gql_model.CreateTierBenefitInput) (*gql_model.TierBenefitResponse, error) {
	// Create tier benefit model
	tierBenefit := &model.TierBenefit{
		TierLevel:                   input.TierLevel,
		TierName:                    input.TierName,
		MinPoints:                   input.MinPoints,
		CashbackPercentage:          decimal.NewFromFloat(input.CashbackPercentage / 100), // Convert from percentage to decimal (divide by 100)
		NetFee:                      decimal.NewFromFloat(input.NetFee / 100),             // Convert from percentage to decimal (divide by 100)
		ReferredIncentivePercentage: decimal.NewFromFloat(0.05),                           // Default 5% referred incentive
		IsActive:                    true,
	}

	// Handle referredIncentivePercentage if provided, otherwise use default 0.05 (5%)
	if input.ReferredIncentivePercentage != nil {
		tierBenefit.ReferredIncentivePercentage = decimal.NewFromFloat(*input.ReferredIncentivePercentage / 100)
	} else {
		tierBenefit.ReferredIncentivePercentage = decimal.NewFromFloat(0.05)
	}

	// Set optional fields
	if input.BenefitsDescription != nil {
		tierBenefit.BenefitsDescription = input.BenefitsDescription
	}
	if input.TierColor != nil {
		tierBenefit.TierColor = input.TierColor
	}
	if input.TierIcon != nil {
		tierBenefit.TierIcon = input.TierIcon
	}

	// Create tier benefit using admin service
	err := r.adminService.CreateTierBenefit(ctx, tierBenefit)
	if err != nil {
		global.GVA_LOG.Error("Failed to create tier benefit", zap.Error(err))
		return &gql_model.TierBenefitResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to create tier benefit: %v", err),
		}, nil
	}

	return &gql_model.TierBenefitResponse{
		Success: true,
		Message: "Tier benefit created successfully",
		Data:    convertTierBenefitToGQL(tierBenefit),
	}, nil
}

func (r *AdminActivityCashbackResolver) AdminGetAllTierBenefits(ctx context.Context, input gql_model.AllTierBenefitsInput) (
	*gql_model.ALlTierBenefitsResponse, error) {
	// Set defaults
	if input.PageSize == nil || *input.Page <= 0 {
		*input.Page = 1
	}

	if input.PageSize == nil || *input.PageSize <= 0 {
		*input.PageSize = 10
	}
	if input.SortOrder == nil || *input.SortOrder == "" {
		*input.SortOrder = "DESC"
	}
	tierBenefits, err := r.adminService.GetTierBenefits(ctx)
	if err != nil {
		global.GVA_LOG.Error("Failed to get all tierBenefits", zap.Error(err))
		return nil, fmt.Errorf("failed to get all tierBenefits: %w", err)
	}

	var gqlTierBenefits []*gql_model.TierBenefit
	for _, category := range tierBenefits {
		gqlTierBenefits = append(gqlTierBenefits, convertTierBenefitToGQL(&category))
	}
	total := len(tierBenefits)
	totalPages := int(math.Ceil(float64(total) / float64(*input.PageSize)))

	return &gql_model.ALlTierBenefitsResponse{

		Data:       gqlTierBenefits,
		Total:      len(tierBenefits),
		Page:       *input.Page,
		PageSize:   *input.PageSize,
		TotalPages: totalPages,
	}, nil

}

// Helper functions to convert model to GraphQL types
func convertActivityTaskToGQL(task *model.ActivityTask) *gql_model.ActivityTask {
	// Convert multilingual name
	var gqlName *gql_model.MultilingualName
	multilingualName := task.GetMultilingualName()
	if multilingualName != nil {
		gqlName = &gql_model.MultilingualName{
			En: multilingualName.En,
			Zh: multilingualName.Zh,
			Ja: multilingualName.Ja,
			Hi: multilingualName.Hi,
			Hk: multilingualName.Hk,
			Vi: multilingualName.Vi,
		}
	}

	gqlTask := &gql_model.ActivityTask{
		ID:   task.ID.String(),
		Name: gqlName,

		Frequency: gql_model.TaskFrequency(task.Frequency), // Direct cast now works since enums match
		Points:    task.Points,
		SortOrder: task.SortOrder,
		IsActive:  task.IsActive,
		CreatedAt: task.CreatedAt,
		UpdatedAt: task.UpdatedAt,
	}

	if task.CategoryID != 0 {
		gqlTask.CategoryID = strconv.FormatUint(uint64(task.CategoryID), 10)
	}

	if task.Description != nil {
		gqlTask.Description = task.Description
	}

	if task.TaskIdentifier != nil {
		taskIdentifier := gql_model.TaskIdentifier(*task.TaskIdentifier)
		gqlTask.TaskIdentifier = &taskIdentifier
	}

	if task.MaxCompletions != nil {
		gqlTask.MaxCompletions = task.MaxCompletions
	}

	if task.ResetPeriod != nil {
		resetPeriodStr := string(*task.ResetPeriod)
		gqlTask.ResetPeriod = &resetPeriodStr
	}

	if task.Conditions != nil {
		conditionsBytes, _ := json.Marshal(task.Conditions)
		conditionsStr := string(conditionsBytes)
		gqlTask.Conditions = &conditionsStr
	}

	if task.ActionTarget != nil {
		gqlTask.ActionTarget = task.ActionTarget
	}

	if task.VerificationMethod != nil {
		verificationMethodStr := string(*task.VerificationMethod)
		gqlTask.VerificationMethod = &verificationMethodStr
	}

	if task.ExternalLink != nil {
		gqlTask.ExternalLink = task.ExternalLink
	}

	if task.TaskIcon != nil {
		gqlTask.TaskIcon = task.TaskIcon
	}

	if task.ButtonText != nil {
		gqlTask.ButtonText = task.ButtonText
	}

	if task.StartDate != nil {
		gqlTask.StartDate = utils.TimeToTimestamp(task.StartDate)
	}

	if task.EndDate != nil {
		gqlTask.EndDate = utils.TimeToTimestamp(task.EndDate)
	}

	// Convert category relationship if available
	if task.Category.ID != 0 {
		gqlTask.Category = convertTaskCategoryToGQL(&task.Category)
	}

	return gqlTask
}

func convertTaskCategoryToGQL(category *model.TaskCategory) *gql_model.TaskCategory {
	gqlCategory := &gql_model.TaskCategory{
		ID:          strconv.FormatUint(uint64(category.ID), 10),
		Name:        convertModelCategoryToAdminGQL(category.Name),
		DisplayName: category.DisplayName,
		IsActive:    category.IsActive,
		SortOrder:   category.SortOrder,
		CreatedAt:   category.CreatedAt,
		UpdatedAt:   category.UpdatedAt,
	}

	if category.Description != nil {
		gqlCategory.Description = category.Description
	}

	if category.Icon != nil {
		gqlCategory.Icon = category.Icon
	}

	return gqlCategory
}

func convertTierBenefitToGQL(tierBenefit *model.TierBenefit) *gql_model.TierBenefit {
	cashbackPercentage, _ := tierBenefit.CashbackPercentage.Float64()
	referredIncentivePercentage, _ := tierBenefit.ReferredIncentivePercentage.Float64()
	netFee, _ := tierBenefit.NetFee.Float64()
	gqlTierBenefit := &gql_model.TierBenefit{
		ID:                          strconv.FormatUint(uint64(tierBenefit.ID), 10),
		TierLevel:                   tierBenefit.TierLevel,
		TierName:                    tierBenefit.TierName,
		MinPoints:                   tierBenefit.MinPoints,
		CashbackPercentage:          cashbackPercentage * 100, // Convert to percentage (multiply by 100)
		ReferredIncentivePercentage: referredIncentivePercentage * 100,
		NetFee:                      netFee * 100, // Convert to percentage (multiply by 100)
		IsActive:                    tierBenefit.IsActive,
		CreatedAt:                   tierBenefit.CreatedAt,
		UpdatedAt:                   tierBenefit.UpdatedAt,
	}

	if tierBenefit.BenefitsDescription != nil {
		gqlTierBenefit.BenefitsDescription = tierBenefit.BenefitsDescription
	}

	if tierBenefit.TierColor != nil {
		gqlTierBenefit.TierColor = tierBenefit.TierColor
	}

	if tierBenefit.TierIcon != nil {
		gqlTierBenefit.TierIcon = tierBenefit.TierIcon
	}

	return gqlTierBenefit
}

// UpdateTierBenefit updates an existing tier benefit (Admin only)
func (r *AdminActivityCashbackResolver) UpdateTierBenefit(ctx context.Context, input gql_model.UpdateTierBenefitInput) (*gql_model.TierBenefitResponse, error) {
	// Parse tier benefit ID as uint
	tierBenefitIDUint, err := strconv.ParseUint(input.ID, 10, 32)
	if err != nil {
		return &gql_model.TierBenefitResponse{
			Success: false,
			Message: "Invalid tier benefit ID",
		}, nil
	}

	// Get existing tier benefit using GetTierBenefits and filter by ID
	allTierBenefits, err := r.adminService.GetTierBenefits(ctx)
	if err != nil {
		global.GVA_LOG.Error("Failed to get tier benefits for update", zap.Error(err))
		return &gql_model.TierBenefitResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to get tier benefits: %v", err),
		}, nil
	}

	var existingTierBenefit *model.TierBenefit
	for _, tierBenefit := range allTierBenefits {
		if tierBenefit.ID == uint(tierBenefitIDUint) {
			existingTierBenefit = &tierBenefit
			break
		}
	}

	if existingTierBenefit == nil {
		return &gql_model.TierBenefitResponse{
			Success: false,
			Message: "Tier benefit not found",
		}, nil
	}

	// Update fields if provided
	if input.TierLevel != nil {
		existingTierBenefit.TierLevel = *input.TierLevel
	}
	if input.TierName != nil {
		existingTierBenefit.TierName = *input.TierName
	}
	if input.MinPoints != nil {
		existingTierBenefit.MinPoints = *input.MinPoints
	}
	if input.CashbackPercentage != nil {
		existingTierBenefit.CashbackPercentage = decimal.NewFromFloat(*input.CashbackPercentage / 100) // Convert from percentage to decimal (divide by 100)
	}
	if input.ReferredIncentivePercentage != nil {
		existingTierBenefit.ReferredIncentivePercentage = decimal.NewFromFloat(*input.ReferredIncentivePercentage / 100)
	}
	if input.NetFee != nil {
		existingTierBenefit.NetFee = decimal.NewFromFloat(*input.NetFee / 100) // Convert from percentage to decimal (divide by 100)
	}
	if input.BenefitsDescription != nil {
		existingTierBenefit.BenefitsDescription = input.BenefitsDescription
	}
	if input.TierColor != nil {
		existingTierBenefit.TierColor = input.TierColor
	}
	if input.TierIcon != nil {
		existingTierBenefit.TierIcon = input.TierIcon
	}
	if input.IsActive != nil {
		existingTierBenefit.IsActive = *input.IsActive
	}

	// Update tier benefit using admin service
	err = r.adminService.UpdateTierBenefit(ctx, existingTierBenefit)
	if err != nil {
		global.GVA_LOG.Error("Failed to update tier benefit", zap.Error(err))
		return &gql_model.TierBenefitResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to update tier benefit: %v", err),
		}, nil
	}

	return &gql_model.TierBenefitResponse{
		Success: true,
		Message: "Tier benefit updated successfully",
		Data:    convertTierBenefitToGQL(existingTierBenefit),
	}, nil
}

// DeleteTierBenefit deletes a tier benefit (Admin only)
func (r *AdminActivityCashbackResolver) DeleteTierBenefit(ctx context.Context, tierBenefitID string) (bool, error) {
	// Parse tier benefit ID as uint
	tierBenefitIDUint, err := strconv.ParseUint(tierBenefitID, 10, 32)
	if err != nil {
		return false, fmt.Errorf("invalid tier benefit ID: %w", err)
	}

	err = r.adminService.DeleteTierBenefit(ctx, uint(tierBenefitIDUint))
	if err != nil {
		global.GVA_LOG.Error("Failed to delete tier benefit", zap.Error(err))
		return false, fmt.Errorf("failed to delete tier benefit: %w", err)
	}

	return true, nil
}

// AdminGetAllTasks retrieves all tasks (Admin only)
func (r *AdminActivityCashbackResolver) AdminGetAllTasks(ctx context.Context) ([]*gql_model.ActivityTask, error) {
	tasks, err := r.adminService.GetAllTasks(ctx)
	if err != nil {
		global.GVA_LOG.Error("Failed to get all tasks", zap.Error(err))
		return nil, fmt.Errorf("failed to get all tasks: %w", err)
	}

	var gqlTasks []*gql_model.ActivityTask
	for _, task := range tasks {
		gqlTasks = append(gqlTasks, convertActivityTaskToGQL(&task))
	}

	return gqlTasks, nil
}
func (r *AdminActivityCashbackResolver) AdminGetAllCategories(ctx context.Context, input gql_model.AllTaskCategoriesInput) (
	*gql_model.ALlTaskCategoriesResponse, error) {
	// Set defaults
	if input.Page == nil || *input.Page <= 0 {
		*input.Page = 1
	}
	if input.PageSize == nil || *input.PageSize <= 0 {
		*input.PageSize = 10
	}
	if input.SortOrder == nil || *input.SortOrder == "" {
		*input.SortOrder = "DESC"
	}
	categories, err := r.adminService.GetTaskCategories(ctx)
	if err != nil {
		global.GVA_LOG.Error("Failed to get all categories", zap.Error(err))
		return nil, fmt.Errorf("failed to get all categories: %w", err)
	}

	var gqlCategories []*gql_model.TaskCategory
	for _, category := range categories {
		gqlCategories = append(gqlCategories, convertTaskCategoryToGQL(&category))
	}
	total := len(categories)
	totalPages := int(math.Ceil(float64(total) / float64(*input.PageSize)))

	return &gql_model.ALlTaskCategoriesResponse{

		Data:       gqlCategories,
		Total:      len(categories),
		Page:       *input.Page,
		PageSize:   *input.PageSize,
		TotalPages: totalPages,
	}, nil

}

// AdminGetTaskCompletionStats retrieves task completion statistics (Admin only)
func (r *AdminActivityCashbackResolver) AdminGetTaskCompletionStats(ctx context.Context, input gql_model.AdminStatsInput) (*gql_model.AdminTaskCompletionStatsResponse, error) {
	stats, err := r.adminService.GetTaskCompletionStats(ctx, input.StartDate, input.EndDate)
	if err != nil {
		global.GVA_LOG.Error("Failed to get task completion stats", zap.Error(err))
		return &gql_model.AdminTaskCompletionStatsResponse{
			Success: false,
			Message: "Failed to retrieve task completion statistics",
		}, nil
	}

	// Convert stats to GraphQL format
	var taskCompletions []*gql_model.TaskCompletionStat
	if taskStats, ok := stats["task_completions"].(map[string]int); ok {
		for taskName, count := range taskStats {
			taskCompletions = append(taskCompletions, &gql_model.TaskCompletionStat{
				TaskName:        taskName,
				CompletionCount: count,
			})
		}
	}

	totalTasks := 0
	if total, ok := stats["total_tasks"].(int); ok {
		totalTasks = total
	}

	return &gql_model.AdminTaskCompletionStatsResponse{
		Success: true,
		Message: "Task completion statistics retrieved successfully",
		Data: &gql_model.AdminTaskCompletionStats{
			TaskCompletions: taskCompletions,
			StartDate:       input.StartDate,
			EndDate:         input.EndDate,
			TotalTasks:      totalTasks,
		},
	}, nil
}

// AdminGetUserActivityStats retrieves user activity statistics (Admin only)
func (r *AdminActivityCashbackResolver) AdminGetUserActivityStats(ctx context.Context, input gql_model.AdminStatsInput) (*gql_model.AdminUserActivityStatsResponse, error) {
	stats, err := r.adminService.GetUserActivityStats(ctx, input.StartDate, input.EndDate)
	if err != nil {
		global.GVA_LOG.Error("Failed to get user activity stats", zap.Error(err))
		return &gql_model.AdminUserActivityStatsResponse{
			Success: false,
			Message: "Failed to retrieve user activity statistics",
		}, nil
	}

	// Convert stats to GraphQL format
	var dailyCompletions []*gql_model.DailyCompletionStat
	if dailyStats, ok := stats["daily_completions"].(map[string]int); ok {
		for date, count := range dailyStats {
			dailyCompletions = append(dailyCompletions, &gql_model.DailyCompletionStat{
				Date:            date,
				CompletionCount: count,
			})
		}
	}

	return &gql_model.AdminUserActivityStatsResponse{
		Success: true,
		Message: "User activity statistics retrieved successfully",
		Data: &gql_model.AdminUserActivityStats{
			DailyCompletions: dailyCompletions,
			StartDate:        input.StartDate,
			EndDate:          input.EndDate,
		},
	}, nil
}

// AdminGetTierDistribution retrieves tier distribution statistics (Admin only)
func (r *AdminActivityCashbackResolver) AdminGetTierDistribution(ctx context.Context) (*gql_model.AdminTierDistributionResponse, error) {
	distribution, err := r.adminService.GetTierDistribution(ctx)
	if err != nil {
		global.GVA_LOG.Error("Failed to get tier distribution", zap.Error(err))
		return &gql_model.AdminTierDistributionResponse{
			Success: false,
			Message: "Failed to retrieve tier distribution",
		}, nil
	}

	var tierStats []*gql_model.TierDistributionStat
	for tierLevel, userCount := range distribution {
		tierStats = append(tierStats, &gql_model.TierDistributionStat{
			TierLevel: tierLevel,
			UserCount: userCount,
		})
	}

	return &gql_model.AdminTierDistributionResponse{
		Success: true,
		Message: "Tier distribution retrieved successfully",
		Data:    tierStats,
	}, nil
}

// AdminGetTopUsers retrieves top users by points (Admin only)
func (r *AdminActivityCashbackResolver) AdminGetTopUsers(ctx context.Context, limit *int) ([]*gql_model.UserTierInfo, error) {
	limitValue := 10 // Default limit
	if limit != nil {
		limitValue = *limit
	}

	users, err := r.adminService.GetTopUsers(ctx, limitValue)
	if err != nil {
		global.GVA_LOG.Error("Failed to get top users", zap.Error(err))
		return nil, fmt.Errorf("failed to get top users: %w", err)
	}

	var gqlUsers []*gql_model.UserTierInfo
	for _, user := range users {
		// Convert decimal fields to float64
		availableCashback, _ := user.ClaimableCashbackUSD.Float64()
		totalCashbackClaimed, _ := user.ClaimedCashbackUSD.Float64()

		gqlUser := &gql_model.UserTierInfo{
			UserID:               user.UserID.String(),
			TotalPoints:          user.TotalPoints,
			AvailableCashback:    availableCashback,
			TotalCashbackClaimed: totalCashbackClaimed,
			CreatedAt:            user.CreatedAt,
		}

		// Set email from User relationship if available
		if user.User.Email != nil {
			gqlUser.Email = user.User.Email
		}

		// Set last activity date
		if user.LastActivityDate != nil {
			gqlUser.LastActivityAt = user.LastActivityDate
		}

		gqlUsers = append(gqlUsers, gqlUser)
	}

	return gqlUsers, nil
}

// Admin reset functions
func (r *AdminActivityCashbackResolver) AdminResetDailyTasks(ctx context.Context) (bool, error) {
	err := r.adminService.ResetAllDailyTasks(ctx)
	if err != nil {
		global.GVA_LOG.Error("Failed to reset daily tasks", zap.Error(err))
		return false, fmt.Errorf("failed to reset daily tasks: %w", err)
	}
	return true, nil
}

func (r *AdminActivityCashbackResolver) AdminResetWeeklyTasks(ctx context.Context) (bool, error) {
	err := r.adminService.ResetAllWeeklyTasks(ctx)
	if err != nil {
		global.GVA_LOG.Error("Failed to reset weekly tasks", zap.Error(err))
		return false, fmt.Errorf("failed to reset weekly tasks: %w", err)
	}
	return true, nil
}

func (r *AdminActivityCashbackResolver) AdminResetMonthlyTasks(ctx context.Context) (bool, error) {
	err := r.adminService.ResetAllMonthlyTasks(ctx)
	if err != nil {
		global.GVA_LOG.Error("Failed to reset monthly tasks", zap.Error(err))
		return false, fmt.Errorf("failed to reset monthly tasks: %w", err)
	}
	return true, nil
}

func (r *AdminActivityCashbackResolver) AdminRecalculateAllUserTiers(ctx context.Context) (bool, error) {
	err := r.adminService.RecalculateAllUserTiers(ctx)
	if err != nil {
		global.GVA_LOG.Error("Failed to recalculate user tiers", zap.Error(err))
		return false, fmt.Errorf("failed to recalculate user tiers: %w", err)
	}
	return true, nil
}

func (r *AdminActivityCashbackResolver) AdminSeedInitialTasks(ctx context.Context) (bool, error) {
	// Check if task seeder is enabled
	if !global.GVA_CONFIG.System.EnableTaskSeeder {
		global.GVA_LOG.Warn("Task seeder is disabled in configuration")
		return false, fmt.Errorf("task seeder is disabled (ENABLE_TASK_SEEDER=false). This is a safety measure to prevent accidental seeding in production")
	}

	err := r.adminService.SeedInitialTasks(ctx)
	if err != nil {
		global.GVA_LOG.Error("Failed to seed initial tasks", zap.Error(err))
		return false, fmt.Errorf("failed to seed initial tasks: %w", err)
	}
	return true, nil
}

// Helper functions to convert between model and admin GraphQL category enums
func convertModelCategoryToAdminGQL(modelCategory model.TaskCategoryName) gql_model.TaskCategoryName {
	switch modelCategory {
	case model.CategoryDaily:
		return gql_model.TaskCategoryNameDaily
	case model.CategoryCommunity:
		return gql_model.TaskCategoryNameCommunity
	case model.CategoryTrading:
		return gql_model.TaskCategoryNameTrading
	default:
		return gql_model.TaskCategoryNameDaily
	}
}

func convertAdminGQLCategoryToModel(adminGQLCategory gql_model.TaskCategoryName) model.TaskCategoryName {
	switch adminGQLCategory {
	case gql_model.TaskCategoryNameDaily:
		return model.CategoryDaily
	case gql_model.TaskCategoryNameCommunity:
		return model.CategoryCommunity
	case gql_model.TaskCategoryNameTrading:
		return model.CategoryTrading
	default:
		return model.CategoryDaily
	}
}

// Helper functions to convert between admin GraphQL frequency enum and model frequency enum
func convertAdminGQLFrequencyToModel(adminGQLFrequency gql_model.TaskFrequency) model.TaskFrequency {
	switch adminGQLFrequency {
	case gql_model.TaskFrequencyOneTime:
		return model.FrequencyOneTime
	case gql_model.TaskFrequencyDaily:
		return model.FrequencyDaily
	case gql_model.TaskFrequencyProgressive:
		return model.FrequencyProgressive
	case gql_model.TaskFrequencyManual:
		return model.FrequencyManual
	case gql_model.TaskFrequencyUnlimited:
		return model.FrequencyUnlimited
	default:
		return model.FrequencyDaily
	}
}

func convertModelFrequencyToAdminGQL(modelFrequency model.TaskFrequency) gql_model.TaskFrequency {
	switch modelFrequency {
	case model.FrequencyOneTime:
		return gql_model.TaskFrequencyOneTime
	case model.FrequencyDaily:
		return gql_model.TaskFrequencyDaily
	case model.FrequencyProgressive:
		return gql_model.TaskFrequencyProgressive
	case model.FrequencyManual:
		return gql_model.TaskFrequencyManual
	case model.FrequencyUnlimited:
		return gql_model.TaskFrequencyUnlimited
	default:
		return gql_model.TaskFrequencyDaily
	}
}

////////////////////////////////////////////

// TierBenefits retrieves all tier benefits
func (r *AdminActivityCashbackResolver) TierBenefits(ctx context.Context) (*gql_model.TierBenefitsResponse, error) {
	adminService := activity_cashback.NewAdminService()

	benefits, err := adminService.GetTierBenefits(ctx)
	if err != nil {
		global.GVA_LOG.Error("Failed to get tier benefits", zap.Error(err))
		return &gql_model.TierBenefitsResponse{
			Success: false,
			Message: "Failed to retrieve tier benefits",
		}, nil
	}

	var gqlBenefits []*gql_model.TierBenefit
	for _, benefit := range benefits {
		gqlBenefits = append(gqlBenefits, convertTierBenefitToGQL(&benefit))
	}

	return &gql_model.TierBenefitsResponse{
		Success: true,
		Message: "Tier benefits retrieved successfully",
		Data:    gqlBenefits,
	}, nil
}

// UserTaskProgress retrieves user task progress
func (r *AdminActivityCashbackResolver) UserTaskProgress(ctx context.Context) (*gql_model.UserTaskProgressResponse, error) {
	userID := ctx.Value("userId")
	if userID == nil {
		return nil, utils.ErrAccessTokenInvalid
	}

	userIDStr, ok := userID.(string)
	if !ok {
		return nil, utils.ErrAccessTokenInvalid
	}

	userUUID, err := uuid.Parse(userIDStr)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID: %w", err)
	}

	service := activity_cashback.NewActivityCashbackService()

	progress, err := service.GetUserTaskProgress(ctx, userUUID)
	if err != nil {
		global.GVA_LOG.Error("Failed to get user task progress", zap.Error(err))
		return &gql_model.UserTaskProgressResponse{
			Success: false,
			Message: "Failed to retrieve task progress",
		}, nil
	}

	var gqlProgress []*gql_model.UserTaskProgress
	for _, p := range progress {
		gqlProgress = append(gqlProgress, convertUserTaskProgressToGQL(&p))
	}

	return &gql_model.UserTaskProgressResponse{
		Success: true,
		Message: "Task progress retrieved successfully",
		Data:    gqlProgress,
	}, nil
}

// TaskCategories retrieves all task categories
func (r *AdminActivityCashbackResolver) TaskCategories(ctx context.Context) ([]*gql_model.TaskCategory, error) {
	adminService := activity_cashback.NewAdminService()

	categories, err := adminService.GetTaskCategories(ctx)
	if err != nil {
		global.GVA_LOG.Error("Failed to get task categories", zap.Error(err))
		return nil, fmt.Errorf("failed to get task categories: %w", err)
	}

	var gqlCategories []*gql_model.TaskCategory
	for _, category := range categories {
		gqlCategories = append(gqlCategories, convertTaskCategoryToGQL(&category))
	}

	return gqlCategories, nil
}

// TasksByCategory retrieves tasks by category name
func (r *AdminActivityCashbackResolver) TasksByCategory(ctx context.Context, categoryName gql_model.TaskCategoryName) ([]*gql_model.ActivityTask, error) {
	service := activity_cashback.NewActivityCashbackService()

	// Convert GraphQL enum to model enum
	modelCategoryName := convertGQLCategoryToModel(categoryName)
	tasks, err := service.GetTasksByCategory(ctx, modelCategoryName)
	if err != nil {
		global.GVA_LOG.Error("Failed to get tasks by category", zap.Error(err))
		return nil, fmt.Errorf("failed to get tasks by category: %w", err)
	}

	var gqlTasks []*gql_model.ActivityTask
	for _, task := range tasks {
		gqlTasks = append(gqlTasks, convertActivityTaskToGQL(&task))
	}

	return gqlTasks, nil
}

func (r *AdminActivityCashbackResolver) TasksByIds(ctx context.Context, input gql_model.TasksByIdsInput) ([]*gql_model.ActivityTask, error) {
	service := activity_cashback.NewActivityCashbackService()
	if len(input.TaskIds) > 10 {
		return nil, fmt.Errorf("cannot request more than 10 tasks at once")
	}
	var tasks []model.ActivityTask
	for i, id := range input.TaskIds {
		taskUUID, err := uuid.Parse(id)
		if err != nil {
			return nil, fmt.Errorf("invalid task ID at position %d: %w", i, err)
		}
		// Get task to determine if it's a community task that requires pending
		task, err := service.GetTaskByID(ctx, taskUUID)
		if err != nil {
			continue
		}
		tasks = append(tasks, *task)
	}

	var gqlTasks []*gql_model.ActivityTask
	for _, task := range tasks {
		gqlTasks = append(gqlTasks, convertActivityTaskToGQL(&task))
	}

	return gqlTasks, nil
}

// UserTierInfo retrieves user tier information
func (r *AdminActivityCashbackResolver) UserTierInfo(ctx context.Context, input gql_model.UserTierInfoInput) (*gql_model.UserTierInfo, error) {
	if input.UserID == "" {
		return nil, utils.ErrAccessTokenInvalid
	}
	userUUID, err := uuid.Parse(input.UserID)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID: %w", err)
	}
	service := activity_cashback.NewActivityCashbackService()

	tierInfo, err := service.GetUserTierInfo(ctx, userUUID)
	if err != nil {
		global.GVA_LOG.Error("Failed to get user tier info", zap.Error(err))
		return nil, fmt.Errorf("failed to get user tier info: %w", err)
	}

	return convertUserTierInfoToGQL(tierInfo), nil
}

// TaskCompletionHistory retrieves task completion history
func (r *AdminActivityCashbackResolver) TaskCompletionHistory(ctx context.Context, input *gql_model.TaskCompletionHistoryInput) (*gql_model.TaskCompletionHistoryResponse, error) {
	if input.UserID == "" {
		return nil, utils.ErrAccessTokenInvalid
	}
	userUUID, err := uuid.Parse(input.UserID)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID: %w", err)
	}

	// Set default values
	limit := 10
	offset := 0
	if input.Limit != nil {
		limit = *input.Limit
	}
	if input.Offset != nil {
		offset = *input.Offset
	}

	// Use factory pattern to get unified repository
	factory := repo_activity_cashback.NewTaskCompletionRepositoryFactory()
	unifiedRepo := factory.GetUnifiedRepository()

	// Get completion history for the user
	completions, err := unifiedRepo.GetByUserID(ctx, userUUID, limit, offset)
	if err != nil {
		global.GVA_LOG.Error("Failed to get task completion history", zap.Error(err))
		return &gql_model.TaskCompletionHistoryResponse{
			Success: false,
			Message: "Failed to retrieve completion history",
		}, nil
	}

	// Convert to GraphQL format
	var gqlHistory []*gql_model.TaskCompletionHistory
	for _, completion := range completions {
		gqlCompletion := &gql_model.TaskCompletionHistory{
			ID:             completion.GetID().String(),
			UserID:         completion.GetUserID().String(),
			TaskID:         completion.GetTaskID().String(),
			PointsAwarded:  completion.GetPointsAwarded(),
			CompletionDate: completion.GetCompletionDate(),
			CreatedAt:      completion.GetCreatedAt(),
		}

		// Add verification data if available
		if verificationData := completion.GetVerificationData(); verificationData != nil {
			verificationJSON, _ := json.Marshal(verificationData)
			verificationStr := string(verificationJSON)
			gqlCompletion.VerificationData = &verificationStr
		}

		// Add task information if available
		task := completion.GetTask()
		if task.ID != uuid.Nil {
			gqlTask := convertActivityTaskToGQL(&task)
			gqlCompletion.Task = gqlTask
		}

		gqlHistory = append(gqlHistory, gqlCompletion)
	}

	return &gql_model.TaskCompletionHistoryResponse{
		Success: true,
		Message: "Completion history retrieved successfully",
		Data:    gqlHistory,
		Total:   len(gqlHistory),
	}, nil
}

// Helper functions to convert between user and admin GraphQL models
func convertUserTaskToAdminTask(userTask *gql_model.ActivityTask) *gql_model.ActivityTask {
	if userTask == nil {
		return nil
	}

	// Convert multilingual name from user to admin format
	var adminName *gql_model.MultilingualName
	if userTask.Name != nil {
		adminName = &gql_model.MultilingualName{
			En: userTask.Name.En,
			Zh: userTask.Name.Zh,
			Ja: userTask.Name.Ja,
			Hi: userTask.Name.Hi,
			Hk: userTask.Name.Hk,
			Vi: userTask.Name.Vi,
		}
	}

	adminTask := &gql_model.ActivityTask{
		ID:          userTask.ID,
		CategoryID:  userTask.CategoryID,
		Name:        adminName,
		Description: userTask.Description,

		Frequency:          gql_model.TaskFrequency(userTask.Frequency), // Direct cast now works since enums match
		Points:             userTask.Points,
		MaxCompletions:     userTask.MaxCompletions,
		ResetPeriod:        userTask.ResetPeriod,
		Conditions:         userTask.Conditions,
		ActionTarget:       userTask.ActionTarget,
		VerificationMethod: userTask.VerificationMethod,
		ExternalLink:       userTask.ExternalLink,
		IsActive:           userTask.IsActive,
		StartDate:          userTask.StartDate,
		EndDate:            userTask.EndDate,
		SortOrder:          userTask.SortOrder,
		CreatedAt:          userTask.CreatedAt,
		UpdatedAt:          userTask.UpdatedAt,
	}

	if userTask.TaskIdentifier != nil {
		taskIdentifier := gql_model.TaskIdentifier(*userTask.TaskIdentifier)
		adminTask.TaskIdentifier = &taskIdentifier
	}

	// Category field removed from user GraphQL schema to prevent circular reference

	return adminTask
}

func convertUserCategoryToAdminCategory(userCategory *gql_model.TaskCategory) *gql_model.TaskCategory {
	if userCategory == nil {
		return nil
	}

	return &gql_model.TaskCategory{
		ID:          userCategory.ID,
		Name:        convertUserGQLCategoryToAdminGQL(userCategory.Name),
		DisplayName: userCategory.DisplayName,
		Description: userCategory.Description,
		IsActive:    userCategory.IsActive,
		SortOrder:   userCategory.SortOrder,
		CreatedAt:   userCategory.CreatedAt,
		UpdatedAt:   userCategory.UpdatedAt,
	}
}

func convertUserTierInfoToAdminGQL(userTierInfo *model.UserTierInfo) *gql_model.UserTierInfo {
	if userTierInfo == nil {
		return nil
	}

	// Convert decimal fields to float64
	availableCashback, _ := userTierInfo.ClaimableCashbackUSD.Float64()
	totalCashbackClaimed, _ := userTierInfo.ClaimedCashbackUSD.Float64()

	adminUserTierInfo := &gql_model.UserTierInfo{
		UserID:               userTierInfo.UserID.String(),
		TotalPoints:          userTierInfo.TotalPoints,
		AvailableCashback:    availableCashback,
		TotalCashbackClaimed: totalCashbackClaimed,
		CreatedAt:            userTierInfo.CreatedAt,
	}

	// Set email from User relationship if available
	if userTierInfo.User.Email != nil {
		adminUserTierInfo.Email = userTierInfo.User.Email
	}

	// Set last activity date
	if userTierInfo.LastActivityDate != nil {
		adminUserTierInfo.LastActivityAt = userTierInfo.LastActivityDate
	}

	// Convert TierBenefit if available
	if userTierInfo.TierBenefit != nil {
		adminUserTierInfo.CurrentTier = convertTierBenefitToAdminGQL(userTierInfo.TierBenefit)
	}

	return adminUserTierInfo
}

func convertTierBenefitToAdminGQL(tierBenefit *model.TierBenefit) *gql_model.TierBenefit {
	if tierBenefit == nil {
		return nil
	}

	// Convert decimal to float64
	cashbackPercentage, _ := tierBenefit.CashbackPercentage.Float64()
	netFee, _ := tierBenefit.NetFee.Float64()

	adminTierBenefit := &gql_model.TierBenefit{
		ID:                 strconv.Itoa(int(tierBenefit.ID)),
		TierLevel:          tierBenefit.TierLevel,
		TierName:           tierBenefit.TierName,
		MinPoints:          tierBenefit.MinPoints,
		CashbackPercentage: cashbackPercentage * 100, // Convert to percentage (multiply by 100)
		NetFee:             netFee * 100,             // Convert to percentage (multiply by 100)
		IsActive:           tierBenefit.IsActive,
		CreatedAt:          tierBenefit.CreatedAt,
		UpdatedAt:          tierBenefit.UpdatedAt,
	}

	if tierBenefit.BenefitsDescription != nil {
		adminTierBenefit.BenefitsDescription = tierBenefit.BenefitsDescription
	}

	if tierBenefit.TierColor != nil {
		adminTierBenefit.TierColor = tierBenefit.TierColor
	}

	if tierBenefit.TierIcon != nil {
		adminTierBenefit.TierIcon = tierBenefit.TierIcon
	}

	return adminTierBenefit
}

// Helper function to convert between user GraphQL enum and admin GraphQL enum
func convertUserGQLCategoryToAdminGQL(userGQLCategory gql_model.TaskCategoryName) gql_model.TaskCategoryName {
	switch userGQLCategory {
	case gql_model.TaskCategoryNameDaily:
		return gql_model.TaskCategoryNameDaily
	case gql_model.TaskCategoryNameCommunity:
		return gql_model.TaskCategoryNameCommunity
	case gql_model.TaskCategoryNameTrading:
		return gql_model.TaskCategoryNameTrading
	default:
		return gql_model.TaskCategoryNameDaily
	}
}

func convertUserGQLFrequencyToAdminGQL(userGQLFrequency gql_model.TaskFrequency) gql_model.TaskFrequency {
	switch userGQLFrequency {
	case gql_model.TaskFrequencyOneTime:
		return gql_model.TaskFrequencyOneTime
	case gql_model.TaskFrequencyDaily:
		return gql_model.TaskFrequencyDaily
	case gql_model.TaskFrequencyProgressive:
		return gql_model.TaskFrequencyProgressive
	case gql_model.TaskFrequencyManual:
		return gql_model.TaskFrequencyManual
	case gql_model.TaskFrequencyUnlimited:
		return gql_model.TaskFrequencyUnlimited
	default:
		return gql_model.TaskFrequencyDaily
	}
}

//////////////////////////////////////////////

// ActivityCashbackDashboard retrieves user dashboard for activity cashback
func (r *AdminActivityCashbackResolver) ActivityCashbackDashboard(ctx context.Context, input *gql_model.ActivityCashbackDashboardInput) (*gql_model.UserDashboardResponse, error) {
	if input.UserID == "" {
		return nil, utils.ErrAccessTokenInvalid
	}
	userUUID, err := uuid.Parse(input.UserID)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID: %w", err)
	}
	// Initialize activity cashback service
	service := activity_cashback.NewActivityCashbackService()

	// Initialize user if needed
	if err := service.InitializeUserForActivityCashback(ctx, userUUID); err != nil {
		global.GVA_LOG.Error("Failed to initialize user for activity cashback", zap.Error(err))
		return &gql_model.UserDashboardResponse{
			Success: false,
			Message: "Failed to initialize user data",
		}, nil
	}

	// Get dashboard data
	dashboard, err := service.GetUserDashboard(ctx, userUUID)
	if err != nil {
		global.GVA_LOG.Error("Failed to get user dashboard", zap.Error(err))
		return &gql_model.UserDashboardResponse{
			Success: false,
			Message: "Failed to retrieve dashboard data",
		}, nil
	}

	// Convert to GraphQL model
	gqlDashboard := convertUserDashboardToGQL(dashboard)

	return &gql_model.UserDashboardResponse{
		Success: true,
		Message: "Dashboard data retrieved successfully",
		Data:    gqlDashboard,
	}, nil
}

// TaskCenter retrieves task center data
func (r *AdminActivityCashbackResolver) TaskCenter(ctx context.Context, input *gql_model.TaskCenterInput) (*gql_model.TaskCenterResponse, error) {
	if input.UserID == "" {
		return nil, utils.ErrAccessTokenInvalid
	}
	userUUID, err := uuid.Parse(input.UserID)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID: %w", err)
	}

	service := activity_cashback.NewActivityCashbackService()

	// Initialize user for activity cashback if needed
	if err := service.InitializeUserForActivityCashback(ctx, userUUID); err != nil {
		global.GVA_LOG.Error("Failed to initialize user for activity cashback", zap.Error(err))
		return &gql_model.TaskCenterResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to initialize user: %s", err.Error()),
		}, nil
	}

	taskCenter, err := service.GetTaskCenter(ctx, userUUID)
	if err != nil {
		global.GVA_LOG.Error("Failed to get task center", zap.Error(err))
		return &gql_model.TaskCenterResponse{
			Success: false,
			Message: "Failed to retrieve task center data",
		}, nil
	}

	// Convert to GraphQL model
	gqlTaskCenter := convertTaskCenterToGQL(taskCenter)

	return &gql_model.TaskCenterResponse{
		Success: true,
		Message: "Task center data retrieved successfully",
		Data:    gqlTaskCenter,
	}, nil
}

// ActivityCashbackSummary retrieves optimized summary data for frontend UI
func (r *AdminActivityCashbackResolver) ActivityCashbackSummary(ctx context.Context, input gql_model.ActivityCashbackSummaryInput) (*gql_model.ActivityCashbackSummaryResponse, error) {
	if input.UserID == "" {
		return nil, utils.ErrAccessTokenInvalid
	}
	userUUID, err := uuid.Parse(input.UserID)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID: %w", err)
	}
	// Initialize activity cashback service
	service := activity_cashback.NewActivityCashbackService()

	// Initialize user if needed
	if err := service.InitializeUserForActivityCashback(ctx, userUUID); err != nil {
		global.GVA_LOG.Error("Failed to initialize user for activity cashback", zap.Error(err))
		return &gql_model.ActivityCashbackSummaryResponse{
			Success: false,
			Message: "Failed to initialize user data",
		}, nil
	}

	// Get summary data
	summary, err := service.GetActivityCashbackSummary(ctx, userUUID)
	if err != nil {
		global.GVA_LOG.Error("Failed to get activity cashback summary", zap.Error(err))
		return &gql_model.ActivityCashbackSummaryResponse{
			Success: false,
			Message: "Failed to retrieve summary data",
		}, nil
	}

	// Convert to GraphQL model
	gqlSummary := convertActivityCashbackSummaryToGQL(summary)

	return &gql_model.ActivityCashbackSummaryResponse{
		Success: true,
		Message: "Summary data retrieved successfully",
		Data:    gqlSummary,
	}, nil
}

// CompleteTask completes a task for the user
func (r *AdminActivityCashbackResolver) CompleteTask(ctx context.Context, input gql_model.CompleteTaskInput) (*gql_model.TaskCompletionResponse, error) {
	if input.UserID == "" {
		return nil, utils.ErrAccessTokenInvalid
	}
	userUUID, err := uuid.Parse(input.UserID)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID: %w", err)
	}

	taskUUID, err := uuid.Parse(input.TaskID)
	if err != nil {
		return &gql_model.TaskCompletionResponse{
			Success: false,
			Message: "Invalid task ID",
		}, nil
	}

	service := activity_cashback.NewActivityCashbackService()

	// Initialize user for activity cashback if needed
	if err := service.InitializeUserForActivityCashback(ctx, userUUID); err != nil {
		global.GVA_LOG.Error("Failed to initialize user for activity cashback", zap.Error(err))
		return &gql_model.TaskCompletionResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to initialize user: %s", err.Error()),
		}, nil
	}

	// Parse verification data
	var verificationData map[string]interface{}
	if input.VerificationData != nil {
		if err := json.Unmarshal([]byte(*input.VerificationData), &verificationData); err != nil {
			return &gql_model.TaskCompletionResponse{
				Success: false,
				Message: "Invalid verification data format",
			}, nil
		}
	}

	// Get task to determine if it's a community task that requires pending
	task, err := service.GetTaskByID(ctx, taskUUID)
	if err != nil {
		global.GVA_LOG.Error("Failed to get task", zap.Error(err))
		return &gql_model.TaskCompletionResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to get task: %s", err.Error()),
		}, nil
	}

	// Check if this is a community task that requires 2-minute wait
	// Use category-based detection for better maintainability
	isCommunityTaskWithWait := task.Category.Name == model.CategoryCommunity &&
		task.TaskIdentifier != nil &&
		model.RequiresTwoMinuteWait(*task.TaskIdentifier)

	if isCommunityTaskWithWait {
		// For ONE_TIME tasks, check if already completed before creating pending task
		if task.Frequency == model.FrequencyOneTime {
			// Get user progress to check completion status
			progress, err := service.GetTaskProgress(ctx, userUUID, taskUUID)
			if err != nil && err.Error() != "record not found" {
				global.GVA_LOG.Error("Failed to get task progress", zap.Error(err))
				return &gql_model.TaskCompletionResponse{
					Success: false,
					Message: fmt.Sprintf("Failed to check task status: %s", err.Error()),
				}, nil
			}

			// If progress exists and task is completed, don't allow re-completion
			if progress != nil && (progress.Status == model.TaskStatusCompleted || progress.Status == model.TaskStatusClaimed) {
				return &gql_model.TaskCompletionResponse{
					Success: false,
					Message: "This task has already been completed",
				}, nil
			}
		}

		// Check if user already has a pending task
		hasPending, err := service.HasPendingCommunityTask(ctx, userUUID, taskUUID)
		if err != nil {
			global.GVA_LOG.Error("Failed to check pending community task", zap.Error(err))
			return &gql_model.TaskCompletionResponse{
				Success: false,
				Message: fmt.Sprintf("Failed to check pending task: %s", err.Error()),
			}, nil
		}

		if hasPending {
			// Get existing pending task to check remaining time
			pendingTask, err := service.GetPendingCommunityTask(ctx, userUUID, taskUUID)
			if err != nil {
				global.GVA_LOG.Error("Failed to get pending community task", zap.Error(err))
			}

			remainingTime := int(120) // Default 2 minutes
			var completionTime *time.Time
			if pendingTask != nil {
				remainingTime = int(pendingTask.GetRemainingWaitTime())
				completionTime = pendingTask.CompletionTime

				// If remaining time is 0 or negative, the task is ready for completion
				// Complete it immediately instead of returning pending response
				if remainingTime <= 0 {
					global.GVA_LOG.Info("Pending task is ready for completion, processing immediately",
						zap.String("user_id", userUUID.String()),
						zap.String("task_id", taskUUID.String()),
						zap.Int("remaining_time", remainingTime))

					// Process the pending task immediately
					if err := service.ProcessSinglePendingTask(ctx, pendingTask); err != nil {
						global.GVA_LOG.Error("Failed to process ready pending task", zap.Error(err))
						return &gql_model.TaskCompletionResponse{
							Success: false,
							Message: fmt.Sprintf("Failed to complete ready task: %s", err.Error()),
						}, nil
					}

					// Get user tier info before completion for tier upgrade check
					tierInfoBefore, err := service.GetUserTierInfo(ctx, userUUID)
					if err != nil {
						global.GVA_LOG.Error("Failed to get user tier info after task completion", zap.Error(err))
					}

					pointsAwarded := task.Points

					// Check if tier was upgraded
					tierUpgraded := false
					newTierLevel := 0
					if tierInfoBefore != nil {
						tierInfoAfter, err := service.GetUserTierInfo(ctx, userUUID)
						if err == nil && tierInfoAfter.CurrentTier > tierInfoBefore.CurrentTier {
							tierUpgraded = true
							newTierLevel = tierInfoAfter.CurrentTier
						}
					}

					return &gql_model.TaskCompletionResponse{
						Success:       true,
						Message:       "Task completed successfully!",
						PointsAwarded: pointsAwarded,
						TierUpgraded:  tierUpgraded,
						NewTierLevel:  &newTierLevel,
						IsPending:     false,
					}, nil
				}
			}

			return &gql_model.TaskCompletionResponse{
				Success:                  true,
				Message:                  "Task is already pending. Please wait for completion.",
				PointsAwarded:            0,
				TierUpgraded:             false,
				IsPending:                true,
				RemainingWaitTimeSeconds: &remainingTime,
				CompletionTime:           completionTime,
			}, nil
		}

		// Create pending community task
		pendingTask, err := service.CreatePendingCommunityTask(ctx, userUUID, taskUUID, verificationData)
		if err != nil {
			global.GVA_LOG.Error("Failed to create pending community task", zap.Error(err))
			return &gql_model.TaskCompletionResponse{
				Success: false,
				Message: fmt.Sprintf("Failed to create pending task: %s", err.Error()),
			}, nil
		}

		remainingTime := int(pendingTask.GetRemainingWaitTime())
		return &gql_model.TaskCompletionResponse{
			Success:                  true,
			Message:                  "Task started! Please wait 2 minutes for completion and points to be awarded.",
			PointsAwarded:            0, // Points will be awarded later
			TierUpgraded:             false,
			IsPending:                true,
			RemainingWaitTimeSeconds: &remainingTime,
			CompletionTime:           pendingTask.CompletionTime,
		}, nil
	}

	// For non-community tasks, proceed with completion
	// Get user tier info before completion
	tierInfoBefore, err := service.GetUserTierInfo(ctx, userUUID)
	if err != nil {
		global.GVA_LOG.Error("Failed to get user tier info before task completion", zap.Error(err))
	}

	// Check if this task should use the task registry system (for tasks with specific handlers)
	if task.TaskIdentifier != nil && (*task.TaskIdentifier == model.TaskIDShareEarningsChart || *task.TaskIdentifier == model.TaskIDConsecutiveCheckinConfigurable) {
		// Use task registry for tasks with specific handlers
		if err := service.ProcessTaskWithRegistry(ctx, userUUID, task, verificationData); err != nil {
			global.GVA_LOG.Error("Failed to process task with registry", zap.Error(err))
			return &gql_model.TaskCompletionResponse{
				Success: false,
				Message: fmt.Sprintf("Failed to complete task: %s", err.Error()),
			}, nil
		}
	} else {
		// Use traditional completion for other tasks
		if err := service.CompleteTask(ctx, userUUID, taskUUID, verificationData); err != nil {
			global.GVA_LOG.Error("Failed to complete task", zap.Error(err))
			return &gql_model.TaskCompletionResponse{
				Success: false,
				Message: fmt.Sprintf("Failed to complete task: %s", err.Error()),
			}, nil
		}
	}

	pointsAwarded := task.Points

	// Check if tier was upgraded
	tierUpgraded := false
	newTierLevel := 0
	if tierInfoBefore != nil {
		tierInfoAfter, err := service.GetUserTierInfo(ctx, userUUID)
		if err == nil && tierInfoAfter.CurrentTier > tierInfoBefore.CurrentTier {
			tierUpgraded = true
			newTierLevel = tierInfoAfter.CurrentTier
		}
	}

	return &gql_model.TaskCompletionResponse{
		Success:                  true,
		Message:                  "Task completed successfully",
		PointsAwarded:            pointsAwarded,
		NewTierLevel:             &newTierLevel,
		TierUpgraded:             tierUpgraded,
		IsPending:                false,
		RemainingWaitTimeSeconds: nil,
		CompletionTime:           nil,
	}, nil
}

// ClaimCashback claims cashback for the user
func (r *AdminActivityCashbackResolver) ClaimCashback(ctx context.Context, input gql_model.ClaimCashbackInput) (*gql_model.CashbackClaimResponse, error) {
	if input.UserID == "" {
		return nil, utils.ErrAccessTokenInvalid
	}
	userUUID, err := uuid.Parse(input.UserID)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID: %w", err)
	}

	service := activity_cashback.NewActivityCashbackService()

	// Initialize user for activity cashback if needed
	if err := service.InitializeUserForActivityCashback(ctx, userUUID); err != nil {
		global.GVA_LOG.Error("Failed to initialize user for activity cashback", zap.Error(err))
		return &gql_model.CashbackClaimResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to initialize user: %s", err.Error()),
		}, nil
	}

	amountUSD := decimal.NewFromFloat(input.AmountUsd)

	// For now, assume 1 SOL = 100 USD (this should come from price service)
	amountSOL := amountUSD.Div(decimal.NewFromInt(100))

	// Create claim
	claim, err := service.CreateClaim(ctx, userUUID, model.ClaimTypeTradingCashback, amountUSD, amountSOL, nil)
	if err != nil {
		global.GVA_LOG.Error("Failed to create cashback claim", zap.Error(err))
		return &gql_model.CashbackClaimResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to create claim: %s", err.Error()),
		}, nil
	}

	// Update user tier info to reflect claimed amount
	if err := service.ClaimCashback(ctx, userUUID, amountUSD); err != nil {
		global.GVA_LOG.Error("Failed to update user cashback", zap.Error(err))
		// Don't return error here as claim is already created
	}

	amountUSDFloat, _ := amountUSD.Float64()
	amountSOLFloat, _ := amountSOL.Float64()

	return &gql_model.CashbackClaimResponse{
		Success:   true,
		Message:   "Cashback claim created successfully",
		ClaimID:   claim.ID.String(),
		AmountUsd: amountUSDFloat,
		AmountSol: amountSOLFloat,
	}, nil
}

// RefreshTaskList refreshes the task list for the user
func (r *AdminActivityCashbackResolver) RefreshTaskList(ctx context.Context) (bool, error) {
	userID := ctx.Value("userId")
	if userID == nil {
		return false, utils.ErrAccessTokenInvalid
	}

	userIDStr, ok := userID.(string)
	if !ok {
		return false, utils.ErrAccessTokenInvalid
	}

	userUUID, err := uuid.Parse(userIDStr)
	if err != nil {
		return false, fmt.Errorf("invalid user ID: %w", err)
	}

	service := activity_cashback.NewActivityCashbackService()

	// Initialize user for activity cashback if needed
	if err := service.InitializeUserForActivityCashback(ctx, userUUID); err != nil {
		global.GVA_LOG.Error("Failed to initialize user for activity cashback", zap.Error(err))
		return false, fmt.Errorf("failed to initialize user: %s", err.Error())
	}

	if err := service.RefreshTaskList(ctx, userUUID); err != nil {
		global.GVA_LOG.Error("Failed to refresh task list", zap.Error(err))
		return false, err
	}

	return true, nil
}

// UserTaskListByCategory retrieves user task progress by category (progress only)
func (r *AdminActivityCashbackResolver) UserTaskListByCategory(ctx context.Context, input gql_model.UserTaskListByCategoryInput) (*gql_model.UserTaskListByCategoryResponse, error) {
	userID := ctx.Value("userId")
	if userID == nil {
		return nil, utils.ErrAccessTokenInvalid
	}

	userIDStr, ok := userID.(string)
	if !ok {
		return nil, utils.ErrAccessTokenInvalid
	}

	userUUID, err := uuid.Parse(userIDStr)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID: %w", err)
	}

	service := activity_cashback.NewActivityCashbackService()

	// Convert GraphQL enum to model enum
	categoryName := convertGQLCategoryToModel(input.CategoryName)
	if !categoryName.IsValid() {
		return &gql_model.UserTaskListByCategoryResponse{
			Success: false,
			Message: "Invalid category name",
			Data:    []*gql_model.TaskWithProgress{},
		}, nil
	}

	// Get user task progress by category with task details
	tasksWithProgress, err := service.GetUserTaskListByCategoryWithDetails(ctx, userUUID, categoryName)
	if err != nil {
		global.GVA_LOG.Error("Failed to get user task list by category", zap.Error(err))
		return &gql_model.UserTaskListByCategoryResponse{
			Success: false,
			Message: "Failed to retrieve user task progress",
			Data:    []*gql_model.TaskWithProgress{},
		}, nil
	}

	// Convert to GraphQL types
	gqlTasksWithProgress := make([]*gql_model.TaskWithProgress, len(tasksWithProgress))
	for i, taskWithProgress := range tasksWithProgress {
		gqlTasksWithProgress[i] = convertTaskWithProgressToGQL(&taskWithProgress)
	}

	return &gql_model.UserTaskListByCategoryResponse{
		Success: true,
		Message: "User task progress retrieved successfully",
		Data:    gqlTasksWithProgress,
	}, nil
}

// Helper functions to convert models to GraphQL types

func convertUserDashboardToGQL(dashboard *activity_cashback.UserDashboard) *gql_model.UserDashboard {
	if dashboard == nil {
		return nil
	}

	// Extract userID for referral bonus calculation
	var userID *uuid.UUID
	if dashboard.UserTierInfo != nil {
		userID = &dashboard.UserTierInfo.UserID
	}

	gqlDashboard := &gql_model.UserDashboard{
		UserTierInfo:     convertUserTierInfoToGQL(dashboard.UserTierInfo),
		TierBenefit:      convertTierBenefitToGQLWithUserID(dashboard.TierBenefit, userID),
		PointsToNextTier: dashboard.PointsToNextTier,
		UserRank:         dashboard.UserRank,
	}

	if dashboard.NextTier != nil {
		gqlDashboard.NextTier = convertTierBenefitToGQLWithUserID(dashboard.NextTier, userID)
	}

	claimableCashback, _ := dashboard.ClaimableCashback.Float64()
	gqlDashboard.ClaimableCashback = claimableCashback

	for _, claim := range dashboard.RecentClaims {
		gqlDashboard.RecentClaims = append(gqlDashboard.RecentClaims, convertActivityCashbackClaimToGQL(&claim))
	}

	return gqlDashboard
}

func convertTaskCenterToGQL(taskCenter *activity_cashback.TaskCenter) *gql_model.TaskCenter {
	if taskCenter == nil {
		return nil
	}

	gqlTaskCenter := &gql_model.TaskCenter{
		CompletedToday:    taskCenter.CompletedToday,
		PointsEarnedToday: taskCenter.PointsEarnedToday,
	}

	for _, category := range taskCenter.Categories {
		gqlCategory := &gql_model.TaskCategoryWithTasks{
			Category: convertTaskCategoryToGQL(&category.Category),
		}

		for _, taskWithProgress := range category.Tasks {
			gqlTaskWithProgress := &gql_model.TaskWithProgress{
				Task: convertActivityTaskToGQL(&taskWithProgress.Task),
			}
			if taskWithProgress.Progress != nil {
				gqlTaskWithProgress.Progress = convertUserTaskProgressToGQL(taskWithProgress.Progress)
			}
			gqlCategory.Tasks = append(gqlCategory.Tasks, gqlTaskWithProgress)
		}

		gqlTaskCenter.Categories = append(gqlTaskCenter.Categories, gqlCategory)
	}

	for _, progress := range taskCenter.UserProgress {
		gqlTaskCenter.UserProgress = append(gqlTaskCenter.UserProgress, convertUserTaskProgressToGQL(&progress))
	}

	for _, streak := range taskCenter.StreakTasks {
		gqlTaskCenter.StreakTasks = append(gqlTaskCenter.StreakTasks, convertUserTaskProgressToGQL(&streak))
	}

	return gqlTaskCenter
}

func convertUserTierInfoToGQL(tierInfo *model.UserTierInfo) *gql_model.UserTierInfo {
	if tierInfo == nil {
		return nil
	}

	// Use effective volume for real-time data (prefers accumulated_volume_usd over trading_volume_usd)
	effectiveVolume := tierInfo.GetEffectiveVolume()
	tradingVolume, _ := effectiveVolume.Float64()
	cumulativeCashback, _ := tierInfo.CumulativeCashbackUSD.Float64()
	claimableCashback, _ := tierInfo.ClaimableCashbackUSD.Float64()
	claimedCashback, _ := tierInfo.ClaimedCashbackUSD.Float64()

	gqlTierInfo := &gql_model.UserTierInfo{
		UserID: tierInfo.UserID.String(),
		//CurrentTier:           tierInfo.CurrentTier,
		TotalPoints:           tierInfo.TotalPoints,
		PointsThisMonth:       tierInfo.PointsThisMonth,
		TradingVolumeUsd:      tradingVolume,
		ActiveDaysThisMonth:   tierInfo.ActiveDaysThisMonth,
		CumulativeCashbackUsd: cumulativeCashback,
		ClaimableCashbackUsd:  claimableCashback,
		ClaimedCashbackUsd:    claimedCashback,
		CreatedAt:             tierInfo.CreatedAt,
		UpdatedAt:             tierInfo.UpdatedAt,
	}

	if tierInfo.LastActivityDate != nil {
		gqlTierInfo.LastActivityDate = tierInfo.LastActivityDate
	}
	if tierInfo.TierUpgradedAt != nil {
		gqlTierInfo.TierUpgradedAt = tierInfo.TierUpgradedAt
	}
	if tierInfo.MonthlyResetAt != nil {
		gqlTierInfo.MonthlyResetAt = tierInfo.MonthlyResetAt
	}
	//if tierInfo.TierBenefit != nil {
	//	gqlTierInfo.TierBenefit = convertTierBenefitToGQLWithUserID(tierInfo.TierBenefit, &tierInfo.UserID)
	//}

	return gqlTierInfo
}

func convertTierBenefitToGQLWithUserID(benefit *model.TierBenefit, userID *uuid.UUID) *gql_model.TierBenefit {
	if benefit == nil {
		return nil
	}

	cashbackPercentage, _ := benefit.CashbackPercentage.Float64()
	referredIncentivePercentage, _ := benefit.ReferredIncentivePercentage.Float64()
	netFee, _ := benefit.NetFee.Float64()

	// Apply 5% referral bonus if user has a direct referrer
	if userID != nil {
		invitationRepo := &agent_referral.InvitationRepository{}
		hasReferrer, err := invitationRepo.HasDirectReferral(context.Background(), *userID)
		if err != nil {
			// Log error but don't fail the conversion - just skip the bonus
			global.GVA_LOG.Error("Failed to check direct referral status for cashback bonus",
				zap.String("userID", userID.String()),
				zap.Error(err))
		} else if hasReferrer {
			// Add 5% bonus to the cashback percentage
			cashbackPercentage += 0.05
		}
	}

	gqlBenefit := &gql_model.TierBenefit{
		ID:                          strconv.Itoa(int(benefit.ID)),
		TierLevel:                   benefit.TierLevel,
		TierName:                    benefit.TierName,
		MinPoints:                   benefit.MinPoints,
		CashbackPercentage:          cashbackPercentage * 100,          // Convert to percentage (multiply by 100)
		ReferredIncentivePercentage: referredIncentivePercentage * 100, // Convert to percentage (multiply by 100)
		NetFee:                      netFee * 100,                      // Convert to percentage (multiply by 100)
		IsActive:                    benefit.IsActive,
		CreatedAt:                   benefit.CreatedAt,
		UpdatedAt:                   benefit.UpdatedAt,
	}

	if benefit.BenefitsDescription != nil {
		gqlBenefit.BenefitsDescription = benefit.BenefitsDescription
	}
	if benefit.TierColor != nil {
		gqlBenefit.TierColor = benefit.TierColor
	}
	if benefit.TierIcon != nil {
		gqlBenefit.TierIcon = benefit.TierIcon
	}

	return gqlBenefit
}

func convertUserTaskProgressToGQL(progress *model.UserTaskProgress) *gql_model.UserTaskProgress {
	if progress == nil {
		return nil
	}

	gqlProgress := &gql_model.UserTaskProgress{
		ID:                 progress.ID.String(),
		UserID:             progress.UserID.String(),
		TaskID:             progress.TaskID.String(),
		Status:             convertTaskStatusToGQL(progress.Status),
		ProgressValue:      progress.ProgressValue,
		CompletionCount:    progress.CompletionCount,
		PointsEarned:       progress.PointsEarned,
		StreakCount:        progress.StreakCount,
		CreatedAt:          progress.CreatedAt,
		UpdatedAt:          progress.UpdatedAt,
		ProgressPercentage: progress.GetProgressPercentage(),
		CanBeClaimed:       progress.CanBeClaimed(),
	}

	if progress.TargetValue != nil {
		gqlProgress.TargetValue = progress.TargetValue
	}
	if progress.LastCompletedAt != nil {
		gqlProgress.LastCompletedAt = progress.LastCompletedAt
	}
	if progress.LastResetAt != nil {
		gqlProgress.LastResetAt = progress.LastResetAt
	}
	if progress.Metadata != nil {
		metadataJSON, _ := json.Marshal(progress.Metadata)
		metadataStr := string(metadataJSON)
		gqlProgress.Metadata = &metadataStr
	}

	return gqlProgress
}

func convertActivityCashbackClaimToGQL(claim *model.ActivityCashbackClaim) *gql_model.ActivityCashbackClaim {
	if claim == nil {
		return nil
	}

	totalAmountUSD, _ := claim.TotalAmountUSD.Float64()
	totalAmountSOL, _ := claim.TotalAmountSOL.Float64()

	gqlClaim := &gql_model.ActivityCashbackClaim{
		ID:             claim.ID.String(),
		UserID:         claim.UserID.String(),
		ClaimType:      convertClaimTypeToGQL(claim.ClaimType),
		TotalAmountUsd: totalAmountUSD,
		TotalAmountSol: totalAmountSOL,
		Status:         convertClaimStatusToGQL(claim.Status),
		ClaimedAt:      claim.ClaimedAt,
		CreatedAt:      claim.CreatedAt,
		UpdatedAt:      claim.UpdatedAt,
	}

	if claim.TransactionHash != nil {
		gqlClaim.TransactionHash = claim.TransactionHash
	}
	if claim.ProcessedAt != nil {
		gqlClaim.ProcessedAt = claim.ProcessedAt
	}
	if claim.Metadata != nil {
		metadataJSON, _ := json.Marshal(claim.Metadata)
		metadataStr := string(metadataJSON)
		gqlClaim.Metadata = &metadataStr
	}

	return gqlClaim
}

// Enum conversion functions

func convertTaskFrequencyToGQL(frequency model.TaskFrequency) gql_model.TaskFrequency {
	switch frequency {
	case model.FrequencyDaily:
		return gql_model.TaskFrequencyDaily
	case model.FrequencyOneTime:
		return gql_model.TaskFrequencyOneTime
	case model.FrequencyUnlimited:
		return gql_model.TaskFrequencyUnlimited
	case model.FrequencyProgressive:
		return gql_model.TaskFrequencyProgressive
	case model.FrequencyManual:
		return gql_model.TaskFrequencyManual
	default:
		return gql_model.TaskFrequencyDaily
	}
}

func convertTaskStatusToGQL(status model.TaskStatus) gql_model.TaskStatus {
	switch status {
	case model.TaskStatusNotStarted:
		return gql_model.TaskStatusNotStarted
	case model.TaskStatusInProgress:
		return gql_model.TaskStatusInProgress
	case model.TaskStatusCompleted:
		return gql_model.TaskStatusCompleted
	case model.TaskStatusClaimed:
		return gql_model.TaskStatusClaimed
	case model.TaskStatusExpired:
		return gql_model.TaskStatusExpired
	default:
		return gql_model.TaskStatusNotStarted
	}
}

func convertClaimTypeToGQL(claimType model.ClaimType) gql_model.ClaimType {
	switch claimType {
	case model.ClaimTypeTradingCashback:
		return gql_model.ClaimTypeTradingCashback
	case model.ClaimTypeTaskReward:
		return gql_model.ClaimTypeTaskReward
	case model.ClaimTypeTierBonus:
		return gql_model.ClaimTypeTierBonus
	case model.ClaimTypeReferralBonus:
		return gql_model.ClaimTypeReferralBonus
	default:
		return gql_model.ClaimTypeTradingCashback
	}
}

func convertClaimStatusToGQL(status model.ClaimStatus) gql_model.ClaimStatus {
	switch status {
	case model.ClaimStatusPending:
		return gql_model.ClaimStatusPending
	case model.ClaimStatusProcessing:
		return gql_model.ClaimStatusProcessing
	case model.ClaimStatusCompleted:
		return gql_model.ClaimStatusCompleted
	case model.ClaimStatusFailed:
		return gql_model.ClaimStatusFailed
	default:
		return gql_model.ClaimStatusPending
	}
}

// convertTaskWithProgressToGQL converts service TaskWithProgress to GraphQL TaskWithProgress
func convertTaskWithProgressToGQL(taskWithProgress *activity_cashback.TaskWithProgress) *gql_model.TaskWithProgress {
	if taskWithProgress == nil {
		return nil
	}

	gqlTask := convertActivityTaskToGQL(&taskWithProgress.Task)
	var gqlProgress *gql_model.UserTaskProgress
	if taskWithProgress.Progress != nil {
		gqlProgress = convertUserTaskProgressToGQL(taskWithProgress.Progress)
	}

	return &gql_model.TaskWithProgress{
		Task:     gqlTask,
		Progress: gqlProgress,
	}
}

// convertActivityCashbackSummaryToGQL converts service ActivityCashbackSummary to GraphQL ActivityCashbackSummary
func convertActivityCashbackSummaryToGQL(summary *activity_cashback.ActivityCashbackSummary) *gql_model.ActivityCashbackSummary {
	if summary == nil {
		return nil
	}

	// Convert decimal values to float64
	accumulatedTradingVolumeUSD, _ := summary.AccumulatedTradingVolumeUSD.Float64()
	accumulatedCashbackUSD, _ := summary.AccumulatedCashbackUSD.Float64()
	claimableCashbackUSD, _ := summary.ClaimableCashbackUSD.Float64()
	claimedCashbackUSD, _ := summary.ClaimedCashbackUSD.Float64()

	gqlSummary := &gql_model.ActivityCashbackSummary{
		// Current ranking info
		CurrentLevel:     summary.CurrentLevel,
		CurrentLevelName: summary.CurrentLevelName,
		NextLevel:        summary.NextLevel,
		NextLevelName:    summary.NextLevelName,

		// Progress calculation
		CurrentScore:           summary.CurrentScore,
		TotalScoreForNextLevel: summary.TotalScoreForNextLevel,
		ScoreRequiredToUpgrade: summary.ScoreRequiredToUpgrade,
		ProgressPercentage:     summary.ProgressPercentage,

		// Trading volume (MEME only for now)
		AccumulatedTradingVolumeUsd: accumulatedTradingVolumeUSD,

		// Activity tracking
		ActiveLogonDays: summary.ActiveLogonDays,

		// Cashback information
		AccumulatedCashbackUsd: accumulatedCashbackUSD,
		ClaimableCashbackUsd:   claimableCashbackUSD,
		ClaimedCashbackUsd:     claimedCashbackUSD,

		// Additional tier info
		CurrentTierColor: summary.CurrentTierColor,
		CurrentTierIcon:  summary.CurrentTierIcon,
		NextTierColor:    summary.NextTierColor,
		NextTierIcon:     summary.NextTierIcon,
	}

	return gqlSummary
}

// Helper functions to convert between model and GraphQL category enums
func convertModelCategoryToGQL(modelCategory model.TaskCategoryName) gql_model.TaskCategoryName {
	switch modelCategory {
	case model.CategoryDaily:
		return gql_model.TaskCategoryNameDaily
	case model.CategoryCommunity:
		return gql_model.TaskCategoryNameCommunity
	case model.CategoryTrading:
		return gql_model.TaskCategoryNameTrading
	default:
		return gql_model.TaskCategoryNameDaily
	}
}

func convertGQLCategoryToModel(gqlCategory gql_model.TaskCategoryName) model.TaskCategoryName {
	switch gqlCategory {
	case gql_model.TaskCategoryNameDaily:
		return model.CategoryDaily
	case gql_model.TaskCategoryNameCommunity:
		return model.CategoryCommunity
	case gql_model.TaskCategoryNameTrading:
		return model.CategoryTrading
	default:
		return model.CategoryDaily
	}
}

// TriggerTierUpgradeCheck manually triggers a tier upgrade check for the current user
func (r *AdminActivityCashbackResolver) TriggerTierUpgradeCheck(ctx context.Context) (*gql_model.TaskCompletionResponse, error) {
	//userID := ctx.Value("userId")
	//if userID == nil {
	//	return nil, utils.ErrAccessTokenInvalid
	//}
	//
	//userIDStr, ok := userID.(string)
	//if !ok {
	//	return nil, utils.ErrAccessTokenInvalid
	//}

	//userUUID, err := uuid.Parse(userIDStr)
	//if err != nil {
	//	return nil, fmt.Errorf("invalid user ID: %w", err)
	//}

	//// Initialize user if not exists
	//if err := r.service.InitializeUserForActivityCashback(ctx, userUUID); err != nil {
	//	global.GVA_LOG.Error("Failed to initialize user for activity cashback", zap.Error(err))
	//	return &gql_model.TaskCompletionResponse{
	//		Success: false,
	//		Message: "Failed to initialize user",
	//	}, nil
	//}
	//
	//// Trigger tier upgrade check
	//newTier, err := r.service.CheckTierUpgrade(ctx, userUUID)
	//if err != nil {
	//	global.GVA_LOG.Error("Failed to check tier upgrade", zap.Error(err))
	//	return &gql_model.TaskCompletionResponse{
	//		Success: false,
	//		Message: fmt.Sprintf("Failed to check tier upgrade: %v", err),
	//	}, nil
	//}
	//
	//if newTier != nil {
	//	return &gql_model.TaskCompletionResponse{
	//		Success:      true,
	//		Message:      fmt.Sprintf("Congratulations! You have been upgraded to %s (Level %d)", newTier.TierName, newTier.TierLevel),
	//		TierUpgraded: true,
	//		NewTierLevel: &newTier.TierLevel,
	//	}, nil
	//}

	return &gql_model.TaskCompletionResponse{
		Success:      true,
		Message:      "No tier upgrade available at this time",
		TierUpgraded: false,
	}, nil
}
